import { useEffect, useState } from 'react'
import UserTable from '../components/UserTable'
import { Plus } from 'lucide-react'
import { toast } from 'sonner'
import type { User } from '../types/user'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { fetchAllUsers } from '../store/slices/usersSlice'

const Users = () => {
  const [activeTab, setActiveTab] = useState<'manager' | 'staff' | 'client'>('manager')
  const dispatch = useAppDispatch()
  const { users, isLoading, error } = useAppSelector((state:any) => state.users)

  useEffect(() => {
    dispatch(fetchAllUsers())
  }, [dispatch])

  useEffect(() => {
    if (error) {
      toast.error(error)
    }
  }, [error])

  

  const tabs = [
    { id: 'manager', label: 'Managers', color: 'blue' },
    { id: 'staff', label: 'Staff', color: 'green' },
    { id: 'client', label: 'Clients', color: 'purple' },
  ] as const

  const filteredUsers = users.filter((user: User) => user.userType === activeTab)

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6 flex-col sm:items-start lg:flex-row">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Users Management</h1>
          <p className="text-gray-600 mt-1">Manage managers, staff, and clients</p>
        </div>
        <div className="flex items-center space-x-3">
         
          <button className="flex items-center space-x-2 bg-[#bf935e] text-white px-4 py-2 rounded-lg hover:cursor-pointer transition-colors">
            <Plus className="w-4 h-4 text-white" />
            <span>Add User</span>
          </button>
        </div>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Tabs */}
      {!isLoading && users.length > 0 && (
        <>
          <div className="mb-6">
            <div className="w-full bg-gray-100 rounded-lg p-1">
              <nav className="grid grid-cols-3 gap-1">
                {tabs.map((tab) => {
                  const isActive = activeTab === tab.id
                  // const userCount = users.filter((user: User) => user.userType === tab.id).length

                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`relative py-3 px-4 rounded-md font-semibold text-sm transition-all duration-200 ${
                        isActive
                          ? 'bg-[#bf935e] text-white shadow-md'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
                      }`}
                    >
                      <div className="flex items-center justify-center space-x-2">
                        <span>{tab.label}</span>
                        {/* <span className={`px-2 py-1 text-xs rounded-full ${
                          isActive
                            ? 'bg-white bg-opacity-20 text-white'
                            : 'bg-gray-200 text-gray-600'
                        }`}>
                          {userCount}
                        </span> */}
                      </div>
                    </button>
                  )
                })}
              </nav>
            </div>
          </div>

          {/* User Table */}
          <UserTable users={filteredUsers} userType={activeTab} />
        </>
      )}

      {/* Empty state */}
      {!isLoading && users.length === 0 && (
        <div className="text-center py-12">
          <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <Plus className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
          <p className="text-gray-500 mb-4">Get started by adding your first user.</p>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Add User
          </button>
        </div>
      )}
    </div>
  )
}

export default Users
