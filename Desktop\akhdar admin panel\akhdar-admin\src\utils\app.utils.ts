import { doc, getDoc } from 'firebase/firestore'
import { db } from '../config/firebase.config'

export async function isLoggedIn(): Promise<boolean> {
  const userId = localStorage.getItem('userId')
  if (!userId) {
    return false
  }
  // CHECK USER IS ADMIN
  const userData = await fetchUser(userId)
  if (userData && userData?.userType === 'admin') {
    return true
  }
  return false
}

// GET USER THROUGH UUID
export async function fetchUser(id: string): Promise<any> {
  const userRef = doc(db, 'Users', id)
  const userSnap = await getDoc(userRef)
  if (userSnap.exists()) {
    return userSnap.data()
  } else return null
}

// GET COUNTS FOR DASHBOARD - Now handled by Redux, but keeping for backward compatibility
export async function getDashboardCounts(): Promise<{
  managers: number
  staff: number
  clients: number
}> {
  // Import the API function to maintain consistency
  const { getDashboardCounts: getCountsFromAPI } = await import('../services/api')
  return getCountsFromAPI()
}
