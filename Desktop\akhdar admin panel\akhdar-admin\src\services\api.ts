import { collection, getDocs, doc, deleteDoc, updateDoc, addDoc, query, where, getCountFromServer } from 'firebase/firestore'
import { db } from '../config/firebase.config'
import type { User } from '../types/user'
import type { Category, CategoryFormData } from '../types/category'

// Fetch dashboard counts from Firestore
export async function getDashboardCounts(): Promise<{
  managers: number
  staff: number
  clients: number
}> {
  try {
    const usersRef = collection(db, 'Users')

    // Get count of managers
    const managersQuery = query(usersRef, where('userType', '==', 'manager'))
    const managersSnapshot = await getCountFromServer(managersQuery)
    const managers = managersSnapshot.data().count

    // Get count of staff
    const staffQuery = query(usersRef, where('userType', '==', 'staff'))
    const staffSnapshot = await getCountFromServer(staffQuery)
    const staff = staffSnapshot.data().count

    // Get count of clients
    const clientsQuery = query(usersRef, where('userType', '==', 'client'))
    const clientsSnapshot = await getCountFromServer(clientsQuery)
    const clients = clientsSnapshot.data().count

    return {
      managers,
      staff,
      clients,
    }
  } catch (error) {
    console.error('Error fetching dashboard counts:', error)
    // Return fallback data if Firebase fails
    return {
      managers: 0,
      staff: 0,
      clients: 0,
    }
  }
}

// Fetch all users from Firestore
export async function fetchUsers(): Promise<User[]> {
  try {
    const usersRef = collection(db, 'Users')
    const querySnapshot = await getDocs(usersRef)

    const users: User[] = []
    querySnapshot.forEach((doc) => {
      const userData = doc.data()
      // Only include users that are not admins
      if (userData.userType !== 'admin') {
        users.push({
          id: doc.id,
          name: userData.name || 'Unknown',
          email: userData.email || '',
          userType: userData.userType || 'client',
          createdAt: userData.createdAt || new Date().toISOString(),
          updatedAt: userData.updatedAt,
        })
      }
    })

    return users
  } catch (error) {
    console.error('Error fetching users:', error)
    throw new Error('Failed to fetch users from database')
  }
}

// Delete a user from Firestore
export async function deleteUser(userId: string): Promise<void> {
  try {
    const userRef = doc(db, 'Users', userId)
    await deleteDoc(userRef)
  } catch (error) {
    console.error('Error deleting user:', error)
    throw new Error('Failed to delete user from database')
  }
}

// Update a user in Firestore
export async function updateUser(userId: string, userData: Partial<User>): Promise<User> {
  try {
    const userRef = doc(db, 'Users', userId)
    const updateData = {
      ...userData,
      updatedAt: new Date().toISOString(),
    }

    // Remove id from update data if present
    const { id, ...dataToUpdate } = updateData as any

    await updateDoc(userRef, dataToUpdate)

    // Return the updated user data
    return {
      id: userId,
      ...userData,
      updatedAt: updateData.updatedAt,
    } as User
  } catch (error) {
    console.error('Error updating user:', error)
    throw new Error('Failed to update user in database')
  }
}

// Fetch users by type
export async function fetchUsersByType(userType: 'manager' | 'staff' | 'client'): Promise<User[]> {
  try {
    const usersRef = collection(db, 'Users')
    const q = query(usersRef, where('userType', '==', userType))
    const querySnapshot = await getDocs(q)

    const users: User[] = []
    querySnapshot.forEach((doc) => {
      const userData = doc.data()
      users.push({
        id: doc.id,
        name: userData.name || 'Unknown',
        email: userData.email || '',
        userType: userData.userType || userType,
        createdAt: userData.createdAt || new Date().toISOString(),
        updatedAt: userData.updatedAt,
      })
    })

    return users
  } catch (error) {
    console.error(`Error fetching ${userType}s:`, error)
    throw new Error(`Failed to fetch ${userType}s from database`)
  }
}

// Categories API Functions

// Fetch all categories from Firestore
export async function fetchCategories(): Promise<Category[]> {
  try {
    const categoriesRef = collection(db, 'Categories')
    const querySnapshot = await getDocs(categoriesRef)

    // Create maps for both parent categories and subcategories
    const categoriesMap = new Map()
    const subcategoriesMap = new Map()

    querySnapshot.forEach((doc) => {
      const categoryData = { id: doc.id, ...doc.data() }

      // If it has a parentCategoryId, it's a subcategory
      if (categoryData.parentCategoryId) {
        if (!subcategoriesMap.has(categoryData.parentCategoryId)) {
          subcategoriesMap.set(categoryData.parentCategoryId, [])
        }
        subcategoriesMap.get(categoryData.parentCategoryId).push(categoryData)
      } else {
        // If it doesn't have a parentCategoryId, it's a parent category
        categoriesMap.set(doc.id, categoryData)
      }
    })

    // Combine the parent categories with their subcategories
    const categoriesWithSubcategories = Array.from(categoriesMap.values()).map((category) => {
      return {
        ...category,
        subcategories: subcategoriesMap.get(category.id) || [],
      }
    })

    return categoriesWithSubcategories
  } catch (error) {
    console.error('Error fetching categories:', error)
    throw new Error('Failed to fetch categories from database')
  }
}

// Add a new category to Firestore
export async function addCategory(categoryData: CategoryFormData): Promise<Category> {
  try {
    const categoriesRef = collection(db, 'Categories')
    const docRef = await addDoc(categoriesRef, {
      ...categoryData,
      createdAt: new Date().toISOString(),
    })

    return {
      id: docRef.id,
      ...categoryData,
      createdAt: new Date().toISOString(),
    }
  } catch (error) {
    console.error('Error adding category:', error)
    throw new Error('Failed to add category to database')
  }
}

// Update a category in Firestore
export async function updateCategory(categoryId: string, categoryData: Partial<Category>): Promise<Category> {
  try {
    const categoryRef = doc(db, 'Categories', categoryId)
    const updateData = {
      ...categoryData,
      updatedAt: new Date().toISOString(),
    }

    // Remove id from update data if present
    const { id, ...dataToUpdate } = updateData as any

    await updateDoc(categoryRef, dataToUpdate)

    return {
      id: categoryId,
      ...categoryData,
      updatedAt: updateData.updatedAt,
    } as Category
  } catch (error) {
    console.error('Error updating category:', error)
    throw new Error('Failed to update category in database')
  }
}
