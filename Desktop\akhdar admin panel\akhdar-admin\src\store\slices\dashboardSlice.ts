import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { getDashboardCounts } from '../../services/api'

export interface DashboardCounts {
  managers: number
  staff: number
  clients: number
}

interface DashboardState {
  counts: DashboardCounts
  isLoading: boolean
  error: string | null
}

const initialState: DashboardState = {
  counts: {
    managers: 0,
    staff: 0,
    clients: 0,
  },
  isLoading: false,
  error: null,
}

// Async thunk for fetching dashboard counts
export const fetchDashboardCounts = createAsyncThunk(
  'dashboard/fetchCounts',
  async (_, { rejectWithValue }) => {
    try {
      const counts = await getDashboardCounts()
      return counts
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch dashboard counts')
    }
  }
)

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    clearError: (state:any) => {
      state.error = null
    },
  },
  extraReducers: (builder:any) => {
    builder
      .addCase(fetchDashboardCounts.pending, (state:any) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchDashboardCounts.fulfilled, (state:any, action:any) => {
        state.isLoading = false
        state.counts = action.payload
        state.error = null
      })
      .addCase(fetchDashboardCounts.rejected, (state:any, action:any) => {
        state.isLoading = false
        state.error = action.payload as string
      })
  },
})

export const { clearError } = dashboardSlice.actions
export default dashboardSlice.reducer
